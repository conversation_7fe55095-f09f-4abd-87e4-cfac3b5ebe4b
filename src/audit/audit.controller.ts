import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { AuditService } from './audit.service';
import { CreateAuditTrailDto } from './dto/create-audit-trail.dto';
import { QueryAuditTrailDto } from './dto/query-audit-trail.dto';
import { AuditTrail } from './entities/audit-trail.entity';

@ApiTags('audit')
@Controller('audit')
export class AuditController {
    constructor(private readonly auditService: AuditService) {}

    @Post()
    @ApiOperation({ summary: 'Create an audit log entry' })
    @ApiResponse({ 
        status: 201, 
        description: 'The audit log has been successfully created.', 
        type: AuditTrail 
    })
    @ApiResponse({ status: 400, description: 'Bad Request.' })
    create(@Body() createAuditTrailDto: CreateAuditTrailDto) {
        return this.auditService.create(createAuditTrailDto);
    }

    @Get()
    @ApiOperation({ summary: 'Get audit logs with filtering and pagination' })
    @ApiResponse({ 
        status: 200, 
        description: 'Return paginated audit logs.',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/AuditTrail' }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' },
                totalPages: { type: 'number' }
            }
        }
    })
    findAll(@Query() query: QueryAuditTrailDto) {
        return this.auditService.findAll(query);
    }

    @Get('recent')
    @ApiOperation({ summary: 'Get recent audit logs for timeline display' })
    @ApiQuery({ name: 'limit', required: false, description: 'Number of recent logs to return', type: Number })
    @ApiResponse({ 
        status: 200, 
        description: 'Return recent audit logs.', 
        type: [AuditTrail] 
    })
    getRecentLogs(@Query('limit') limit?: number) {
        return this.auditService.getRecentLogs(limit ? parseInt(limit.toString()) : 10);
    }

    @Get('statistics')
    @ApiOperation({ summary: 'Get audit statistics' })
    @ApiQuery({ name: 'days', required: false, description: 'Number of days to include in statistics', type: Number })
    @ApiResponse({ 
        status: 200, 
        description: 'Return audit statistics.',
        schema: {
            type: 'object',
            properties: {
                totalLogs: { type: 'number' },
                moduleStats: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            module: { type: 'string' },
                            count: { type: 'number' }
                        }
                    }
                },
                actionStats: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            action: { type: 'string' },
                            count: { type: 'number' }
                        }
                    }
                },
                topUsers: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            username: { type: 'string' },
                            count: { type: 'number' }
                        }
                    }
                },
                period: { type: 'string' }
            }
        }
    })
    getStatistics(@Query('days') days?: number) {
        return this.auditService.getStatistics(days ? parseInt(days.toString()) : 30);
    }

    @Get(':id')
    @ApiOperation({ summary: 'Get a specific audit log by ID' })
    @ApiParam({ name: 'id', description: 'Audit log ID' })
    @ApiResponse({ 
        status: 200, 
        description: 'Return the audit log.', 
        type: AuditTrail 
    })
    @ApiResponse({ status: 404, description: 'Audit log not found.' })
    findOne(@Param('id') id: string) {
        return this.auditService.findOne(id);
    }
}
