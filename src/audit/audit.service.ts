import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { AuditTrail, AuditAction, AuditModule } from './entities/audit-trail.entity';
import { CreateAuditTrailDto } from './dto/create-audit-trail.dto';
import { QueryAuditTrailDto } from './dto/query-audit-trail.dto';
import { User } from '../users/entities/user.entity';

export interface AuditLogOptions {
    module: AuditModule;
    action: AuditAction;
    entityId?: string;
    entityName?: string;
    description?: string;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    user?: User;
    userId?: string;
    username?: string;
    userFullName?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: string;
}

export interface PaginatedAuditResult {
    data: AuditTrail[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}

@Injectable()
export class AuditService {
    private readonly logger = new Logger(AuditService.name);

    constructor(
        @InjectRepository(AuditTrail)
        private readonly auditRepository: Repository<AuditTrail>,
    ) {}

    /**
     * Create an audit log entry
     */
    async log(options: AuditLogOptions): Promise<AuditTrail> {
        try {
            const auditEntry = this.auditRepository.create({
                module: options.module,
                action: options.action,
                entityId: options.entityId,
                entityName: options.entityName,
                description: options.description,
                oldValues: options.oldValues,
                newValues: options.newValues,
                userId: options.userId || options.user?.id,
                username: options.username || options.user?.username,
                userFullName: options.userFullName || 
                    (options.user ? `${options.user.firstName} ${options.user.lastName}`.trim() : undefined),
                ipAddress: options.ipAddress,
                userAgent: options.userAgent,
                metadata: options.metadata,
                user: options.user,
            });

            const savedEntry = await this.auditRepository.save(auditEntry);
            this.logger.log(`Audit log created: ${options.module}.${options.action} by ${options.username || 'system'}`);
            return savedEntry;
        } catch (error) {
            this.logger.error(`Failed to create audit log: ${error.message}`, error.stack);
            // Don't throw error to avoid breaking the main operation
            return null;
        }
    }

    /**
     * Create audit log from DTO
     */
    async create(createAuditTrailDto: CreateAuditTrailDto): Promise<AuditTrail> {
        const auditEntry = this.auditRepository.create(createAuditTrailDto);
        return this.auditRepository.save(auditEntry);
    }

    /**
     * Find audit logs with pagination and filtering
     */
    async findAll(query: QueryAuditTrailDto): Promise<PaginatedAuditResult> {
        const {
            module,
            action,
            userId,
            username,
            entityId,
            entityName,
            startDate,
            endDate,
            page = 1,
            limit = 20,
            sortBy = 'createdAt',
            sortOrder = 'desc',
            search
        } = query;

        const queryBuilder: SelectQueryBuilder<AuditTrail> = this.auditRepository
            .createQueryBuilder('audit')
            .leftJoinAndSelect('audit.user', 'user');

        // Apply filters
        if (module) {
            queryBuilder.andWhere('audit.module = :module', { module });
        }

        if (action) {
            queryBuilder.andWhere('audit.action = :action', { action });
        }

        if (userId) {
            queryBuilder.andWhere('audit.userId = :userId', { userId });
        }

        if (username) {
            queryBuilder.andWhere('audit.username ILIKE :username', { username: `%${username}%` });
        }

        if (entityId) {
            queryBuilder.andWhere('audit.entityId = :entityId', { entityId });
        }

        if (entityName) {
            queryBuilder.andWhere('audit.entityName ILIKE :entityName', { entityName: `%${entityName}%` });
        }

        if (startDate) {
            queryBuilder.andWhere('audit.createdAt >= :startDate', { startDate });
        }

        if (endDate) {
            queryBuilder.andWhere('audit.createdAt <= :endDate', { endDate });
        }

        if (search) {
            queryBuilder.andWhere(
                '(audit.description ILIKE :search OR audit.entityName ILIKE :search OR audit.username ILIKE :search)',
                { search: `%${search}%` }
            );
        }

        // Apply sorting
        const validSortFields = ['createdAt', 'module', 'action', 'username', 'entityName'];
        const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
        queryBuilder.orderBy(`audit.${sortField}`, sortOrder.toUpperCase() as 'ASC' | 'DESC');

        // Apply pagination
        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);

        // Execute query
        const [data, total] = await queryBuilder.getManyAndCount();

        return {
            data,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }

    /**
     * Find a single audit log by ID
     */
    async findOne(id: string): Promise<AuditTrail> {
        return this.auditRepository.findOne({
            where: { id },
            relations: ['user'],
        });
    }

    /**
     * Get recent audit logs for timeline display
     */
    async getRecentLogs(limit: number = 10): Promise<AuditTrail[]> {
        return this.auditRepository.find({
            relations: ['user'],
            order: { createdAt: 'DESC' },
            take: limit,
        });
    }

    /**
     * Get audit statistics
     */
    async getStatistics(days: number = 30): Promise<any> {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        const queryBuilder = this.auditRepository.createQueryBuilder('audit')
            .where('audit.createdAt >= :startDate', { startDate });

        const [
            totalLogs,
            moduleStats,
            actionStats,
            userStats
        ] = await Promise.all([
            queryBuilder.getCount(),
            queryBuilder
                .select('audit.module', 'module')
                .addSelect('COUNT(*)', 'count')
                .groupBy('audit.module')
                .getRawMany(),
            queryBuilder
                .select('audit.action', 'action')
                .addSelect('COUNT(*)', 'count')
                .groupBy('audit.action')
                .getRawMany(),
            queryBuilder
                .select('audit.username', 'username')
                .addSelect('COUNT(*)', 'count')
                .where('audit.username IS NOT NULL')
                .groupBy('audit.username')
                .orderBy('COUNT(*)', 'DESC')
                .limit(10)
                .getRawMany()
        ]);

        return {
            totalLogs,
            moduleStats,
            actionStats,
            topUsers: userStats,
            period: `${days} days`
        };
    }
}
